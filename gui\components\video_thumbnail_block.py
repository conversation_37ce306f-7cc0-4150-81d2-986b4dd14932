#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import cv2
from pathlib import Path
from PySide6.QtWidgets import QWidget
from PySide6.QtCore import Qt, Signal, QPoint, QRect, QTimer
from PySide6.QtGui import QPainter, QColor, QPen, QBrush, QPixmap, QFont, QPolygon

class VideoThumbnailBlock(QWidget):
    """显示视频缩略图的媒体块"""

    def __init__(self, media_item, track_index, media_index, timeline, video_processor=None):
        super().__init__()
        self.media_item = media_item
        self.track_index = track_index
        self.media_index = media_index
        self.timeline = timeline
        self.video_processor = video_processor
        self.dragging = False
        self.drag_start_pos = None
        self.original_pos = None

        # 检查是否是占位符
        self.is_placeholder = False
        if isinstance(media_item, dict):
            file_path = media_item.get('file_path', '')
            self.is_placeholder = (file_path.startswith('placeholder_') or
                                 media_item.get('is_placeholder', False))

        # 缩略图缓存管理
        self._thumbnail_cache = {}
        self._max_cache_size = 50  # 最大缓存数量
        self._cache_access_count = {}  # 访问计数，用于LRU清理

        # 设置基本属性 - 增加高度
        self.setMinimumHeight(84)  # 适应更大的轨道高度

        # 缩略图配置 - 按原比例显示
        self.thumbnail_width = 100  # 基础缩略图宽度
        self.thumbnail_height = 60  # 基础高度，实际会根据视频比例调整

        # 异步加载相关
        self._loading_thumbnails = {}  # 正在加载的缩略图 {index: timestamp}
        self._loaded_thumbnails = {}   # 已加载的缩略图 {index: QPixmap}
        self._load_queue = []          # 加载队列
        self._current_timestamps = []  # 当前应该显示的时间戳

        # 创建加载定时器
        self._load_timer = QTimer()
        self._load_timer.timeout.connect(self._load_next_thumbnail)
        self._load_timer.setSingleShot(True)

        # 裁剪游标相关 - 完全按照原版
        self.trim_handle_width = 24  # 游标宽度，增加宽度使其更容易点击和看到
        self.left_trim_dragging = False
        self.right_trim_dragging = False
        self.trim_drag_start_pos = None

        # 裁剪位置（相对于素材内部的像素位置）
        self.left_trim_pos = 0  # 左裁剪位置（像素）
        self.right_trim_pos = 0  # 右裁剪位置（像素，从右边缘开始）

        # 预览裁剪位置（拖拽时的临时状态）
        self.preview_left_trim_pos = 0
        self.preview_right_trim_pos = 0

        # 裁剪历史记录（支持往外拖恢复）
        self.trim_history = []
        self.current_trim_index = -1

        # 设置鼠标跟踪
        self.setMouseTracking(True)

        # 🔧 新增：启用右键菜单
        self.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

    def get_pixels_per_second(self):
        """安全获取pixels_per_second值"""
        # 🔧 修复：通过global_params访问pixels_per_second
        if hasattr(self.timeline, 'global_params') and hasattr(self.timeline.global_params, 'pixels_per_second'):
            return self.timeline.global_params.pixels_per_second
        elif hasattr(self.timeline, 'pixels_per_second'):
            return self.timeline.pixels_per_second
        else:
            return 100  # 默认值

    def paintEvent(self, event):
        """绘制缩略图或占位符"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.RenderHint.Antialiasing)

        rect = self.rect()

        # 先清除整个背景，防止重影
        painter.fillRect(rect, QColor(10, 10, 10))  # 轨道背景色

        # 根据轨道类型设置背景色 - 现代化主题
        track = self.timeline.tracks[self.track_index]
        if track['type'] == 'video':
            bg_color = QColor(40, 40, 50)  # 深蓝灰色
        else:
            bg_color = QColor(35, 35, 45)   # 深紫灰色

        # 如果有video_processor且是视频文件，尝试显示缩略图
        if (self.video_processor and
            isinstance(self.media_item, dict) and
            track['type'] == 'video' and
            self.width() > 80):

            if self.draw_video_thumbnails(painter, rect):
                # 启用裁剪预览，显示灰色遮罩
                self.draw_trim_preview(painter, rect)
                self.draw_text_overlay(painter, rect)
                # 游标绘制仍然由轨道层级统一处理，确保在最顶层
                return

        # 如果没有缩略图，绘制简单的媒体块
        painter.fillRect(rect, bg_color)

        # 检查是否是占位符
        is_placeholder = (isinstance(self.media_item, dict) and
                         (self.media_item.get('file_path', '').startswith('placeholder_') or
                          self.media_item.get('is_placeholder', False)))

        # 如果是占位符，使用特殊样式
        if is_placeholder:
            # 占位符背景
            painter.fillRect(rect, QColor(60, 60, 80, 200))  # 半透明紫色
            painter.setPen(QPen(QColor(150, 150, 200), 2, Qt.PenStyle.DashLine))
            painter.drawRect(rect.adjusted(2, 2, -2, -2))

            # 占位符图标和文本
            painter.setPen(QColor(200, 200, 255))
            font = QFont("Arial", 12, QFont.Weight.Bold)
            painter.setFont(font)

            # 绘制大图标
            icon_rect = QRect(rect.x() + 10, rect.y() + 10, 40, 40)
            painter.drawText(icon_rect, Qt.AlignmentFlag.AlignCenter, "📁")

            # 绘制提示文本
            text_rect = QRect(rect.x() + 55, rect.y() + 10, rect.width() - 65, rect.height() - 20)
            placeholder_text = self.media_item.get('placeholder_text', '拖入视频文件')
            painter.drawText(text_rect, Qt.AlignmentFlag.AlignLeft | Qt.AlignmentFlag.AlignVCenter, placeholder_text)
        else:
            # 普通媒体块
            painter.setPen(QColor(255, 255, 255))
            font = QFont("Arial", 24)
            painter.setFont(font)
            painter.drawText(rect, Qt.AlignmentFlag.AlignCenter, "🎬")

        # 绘制文本叠加
        self.draw_text_overlay(painter, rect)

        # 绘制裁剪预览遮罩
        self.draw_trim_preview(painter, rect)

        # 结束绘制
        painter.end()

    def draw_video_thumbnails(self, painter, rect):
        """绘制视频缩略图 - 考虑裁剪偏移的帧选择逻辑"""
        try:
            if not isinstance(self.media_item, dict):
                return False

            file_path = self.media_item['file_path']

            # 检查是否是占位符文件
            if file_path.startswith('placeholder_') or self.media_item.get('is_placeholder', False):
                return False  # 占位符不绘制缩略图，让paintEvent处理

            # 检查文件是否存在
            if not os.path.exists(file_path):
                return False

            duration = self.media_item['duration']
            track_width = rect.width()

            # 获取裁剪偏移信息
            trim_start = self.media_item.get('trim_start', 0)

            # 核心逻辑：根据轨道宽度计算能放下多少帧
            thumb_count = max(1, track_width // self.thumbnail_width)

            # 如果轨道太窄，只显示1帧（中间帧）
            if track_width < self.thumbnail_width:
                thumb_count = 1

            # 计算缩略图尺寸
            thumb_width = min(self.thumbnail_width, track_width // thumb_count)
            thumb_height = min(self.thumbnail_height, rect.height() - 4)

            # 计算时间戳 - 考虑裁剪偏移
            timestamps = []
            if thumb_count == 1:
                # 只有一帧时，显示中间帧（考虑裁剪偏移）
                middle_time = trim_start + duration / 2
                timestamps = [middle_time]
            else:
                # 多帧时，均匀分布（考虑裁剪偏移）
                for i in range(thumb_count):
                    # 在可见时长内均匀分布
                    visible_duration = duration  # 这里的duration已经是裁剪后的时长
                    time_offset = (i + 0.5) * visible_duration / thumb_count
                    timestamp = trim_start + time_offset
                    timestamps.append(timestamp)

            # 生成缓存键 - 包含轨道宽度确保正确更新
            cache_key = f"{file_path}_{thumb_count}_{thumb_width}x{thumb_height}_{track_width}"

            # 异步加载缩略图
            self._current_timestamps = timestamps
            cache_key_base = f"{file_path}_{thumb_width}x{thumb_height}_{track_width}"

            # 检查是否需要重新加载
            if (not hasattr(self, '_last_cache_key') or
                self._last_cache_key != cache_key_base):
                self._last_cache_key = cache_key_base
                self._start_async_loading(timestamps, thumb_width, thumb_height)

            # 使用已加载的缩略图
            thumbnails = [self._loaded_thumbnails.get(i) for i in range(len(timestamps))]

            # 绘制缩略图 - 严格按照时间轴位置绘制
            if thumbnails:
                thumb_y = (rect.height() - thumb_height) // 2  # 垂直居中

                for i, thumbnail in enumerate(thumbnails):
                    if i >= len(timestamps):
                        break

                    if thumbnail and not thumbnail.isNull():
                        # 计算缩略图位置
                        thumb_x = i * thumb_width
                        thumb_rect = QRect(thumb_x, thumb_y, thumb_width, thumb_height)

                        # 绘制缩略图
                        painter.drawPixmap(thumb_rect, thumbnail)

                        # 绘制边框
                        painter.setPen(QPen(QColor(255, 255, 255, 100), 1))
                        painter.drawRect(thumb_rect)

                return True

        except Exception as e:
            print(f"绘制视频缩略图失败: {e}")
            return False

        return False

    def draw_text_overlay(self, painter, rect):
        """在缩略图上绘制文本叠加"""
        if isinstance(self.media_item, dict):
            name = self.media_item.get('name', 'Unknown')
            duration = self.media_item.get('duration', 0)
        else:
            name = 'Media'
            duration = 0

        # 绘制半透明背景
        overlay_rect = QRect(2, rect.height() - 16, rect.width() - 4, 14)
        painter.fillRect(overlay_rect, QColor(0, 0, 0, 160))

        # 绘制文本
        painter.setPen(QColor(255, 255, 255))
        font = QFont("Arial", 8, QFont.Weight.Bold)
        painter.setFont(font)

        text = f"{name[:8]}... {duration:.1f}s" if len(name) > 8 else f"{name} {duration:.1f}s"
        painter.drawText(overlay_rect, Qt.AlignmentFlag.AlignCenter, text)

    def draw_trim_preview(self, painter, rect):
        """绘制裁剪预览遮罩 - 完全按照原版逻辑"""
        # 如果正在拖拽裁剪游标，显示预览效果
        if self.left_trim_dragging or self.right_trim_dragging:
            # 使用预览位置
            left_trim = self.preview_left_trim_pos
            right_trim = self.preview_right_trim_pos
        else:
            # 使用实际位置
            left_trim = self.left_trim_pos
            right_trim = self.right_trim_pos

        # 绘制左侧遮罩
        if left_trim > 0:
            mask_rect = QRect(0, 0, left_trim, rect.height())
            painter.fillRect(mask_rect, QColor(0, 0, 0, 120))

        # 绘制右侧遮罩
        if right_trim > 0:
            mask_rect = QRect(rect.width() - right_trim, 0, right_trim, rect.height())
            painter.fillRect(mask_rect, QColor(0, 0, 0, 120))

    def _start_async_loading(self, timestamps, width, height):
        """开始异步加载缩略图 - 启用真实视频帧渲染"""
        # 先立即生成占位符，确保UI响应
        for i, timestamp in enumerate(timestamps):
            self._loaded_thumbnails[i] = self._generate_placeholder_thumbnail(width, height)

        # 设置加载队列，异步加载真实帧
        self._load_queue = [(i, timestamp, width, height) for i, timestamp in enumerate(timestamps)]
        self._current_load_params = (width, height)

        # 启动异步加载定时器
        if self._load_queue and not self._load_timer.isActive():
            self._load_timer.start(100)  # 100ms后开始加载第一帧

    def _load_next_thumbnail(self):
        """加载下一个缩略图（定时器回调）- 真正的异步加载"""
        if not self._load_queue:
            return

        # 取出队列中的第一个任务
        i, timestamp, width, height = self._load_queue.pop(0)

        try:
            # 尝试生成真实的视频帧（在定时器回调中，不会阻塞UI）
            thumbnail = self._generate_real_thumbnail(self.media_item['file_path'], timestamp, width, height)
            if thumbnail:
                self._loaded_thumbnails[i] = thumbnail
                # 更新显示
                self.update()
        except Exception as e:
            print(f"异步加载缩略图失败: {e}")

        # 如果还有更多任务，继续加载
        if self._load_queue:
            self._load_timer.start(100)  # 100ms后加载下一帧，避免过于频繁

    def _generate_placeholder_thumbnail(self, width, height):
        """生成占位符缩略图"""
        pixmap = QPixmap(width, height)
        pixmap.fill(QColor(60, 60, 60))
        
        painter = QPainter(pixmap)
        painter.setPen(QColor(255, 255, 255))
        font = QFont("Arial", 16)
        painter.setFont(font)
        painter.drawText(pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "🎬")
        painter.end()
        
        return pixmap

    def _generate_real_thumbnail(self, file_path, timestamp, width, height):
        """生成真实的视频帧缩略图 - 线程安全版本"""
        import threading
        import time

        # 使用文件锁确保同一时间只有一个线程访问同一个视频文件
        lock_key = f"video_lock_{file_path}"
        if not hasattr(self.__class__, '_video_locks'):
            self.__class__._video_locks = {}

        if lock_key not in self.__class__._video_locks:
            self.__class__._video_locks[lock_key] = threading.Lock()

        video_lock = self.__class__._video_locks[lock_key]

        try:
            # 尝试获取锁，最多等待0.1秒
            if video_lock.acquire(timeout=0.1):
                try:
                    return self._safe_generate_thumbnail(file_path, timestamp, width, height)
                finally:
                    video_lock.release()
            else:
                # 如果无法获取锁，返回占位符
                print(f"⚠️ 视频文件被占用，使用占位符: {timestamp:.2f}s -> {width}x{height}")
                return None
        except Exception as e:
            print(f"⚠️ 生成视频帧失败: {e}")
            return None

    def _safe_generate_thumbnail(self, file_path, timestamp, width, height):
        """安全生成视频帧的内部方法"""
        try:
            import cv2

            # 使用OpenCV生成视频帧
            cap = cv2.VideoCapture(file_path)
            if not cap.isOpened():
                return None

            # 快速跳转到指定时间
            cap.set(cv2.CAP_PROP_POS_MSEC, timestamp * 1000)
            ret, frame = cap.read()
            cap.release()  # 立即释放资源

            if ret and frame is not None:
                # 转换BGR到RGB
                frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)

                # 转换为QPixmap
                from PySide6.QtGui import QImage
                h, w, ch = frame_rgb.shape
                bytes_per_line = ch * w
                q_image = QImage(frame_rgb.data, w, h, bytes_per_line, QImage.Format.Format_RGB888)
                pixmap = QPixmap.fromImage(q_image)

                # 缩放到目标尺寸
                scaled_pixmap = pixmap.scaled(width, height, Qt.AspectRatioMode.KeepAspectRatio, Qt.TransformationMode.SmoothTransformation)
                print(f"✅ 成功生成视频帧: {timestamp:.2f}s -> {width}x{height}")
                return scaled_pixmap

        except ImportError:
            print("⚠️ OpenCV未安装，无法生成真实视频帧")
        except Exception as e:
            print(f"⚠️ OpenCV生成缩略图失败: {e}")

        return None

    def mousePressEvent(self, event):
        """鼠标按下事件 - 完全按照原版逻辑"""
        print(f"🔍 mousePressEvent: 鼠标位置={event.pos()}")
        print(f"   - 素材块位置: {self.pos()}")
        print(f"   - 素材块大小: {self.size()}")

        if event.button() == Qt.MouseButton.LeftButton:
            # 检查是否点击了裁剪游标
            print(f"   - 开始检测游标...")
            left_trim_hit = self.is_on_left_trim_handle(event.pos())
            right_trim_hit = self.is_on_right_trim_handle(event.pos())

            print(f"   - 左游标检测: {left_trim_hit}")
            print(f"   - 右游标检测: {right_trim_hit}")

            if left_trim_hit:
                self.left_trim_dragging = True
                self.trim_drag_start_pos = event.pos()
                # 保存当前裁剪状态到历史记录
                self.save_trim_state()
                print("✅ 开始左侧裁剪（预览模式）")
                return
            elif right_trim_hit:
                self.right_trim_dragging = True
                self.trim_drag_start_pos = event.pos()
                # 保存当前裁剪状态到历史记录
                self.save_trim_state()
                print("✅ 开始右侧裁剪（预览模式）")
                return

            print("   - 普通拖拽模式")
            # 普通拖拽
            self.drag_start_pos = event.pos()
            self.original_pos = self.pos()

            # 🔧 修复：在素材块点击时也处理时间轴跳转
            if not self.dragging and isinstance(self.media_item, dict):
                # 计算点击位置相对于轨道的时间
                click_x = self.x() + event.pos().x()  # 素材块在轨道中的绝对位置
                pixels_per_second = getattr(self.timeline, 'pixels_per_second', 100)
                time_pos = click_x / pixels_per_second

                # 查找MultiTrackTimeline
                timeline = self.timeline
                while timeline:
                    if timeline.__class__.__name__ == 'MultiTrackTimeline':
                        break
                    timeline = timeline.parent()

                if timeline and hasattr(timeline, 'on_timeline_position_changed'):
                    timeline.on_timeline_position_changed(time_pos)
                    print(f"🎯 素材块点击，移动时间轴到: {time_pos:.2f}s")

                # 素材块被点击 - 通知时间轴处理媒体点击
                file_path = self.media_item['file_path']
                start_time = self.media_item.get('start_time', 0)
                print(f"🎬 视频素材块被点击: {file_path} (起点: {start_time:.2f}s)")

                # 查找MultiTrackTimeline并调用媒体点击处理
                timeline = self.timeline
                while timeline:
                    if timeline.__class__.__name__ == 'MultiTrackTimeline':
                        break
                    timeline = timeline.parent()

                if timeline and hasattr(timeline, 'on_media_clicked'):
                    timeline.on_media_clicked(file_path, start_time, 'video')

    def mouseMoveEvent(self, event):
        """鼠标移动事件 - 完全按照原版逻辑"""
        # 处理裁剪游标拖拽 - 剪映风格预览模式
        if self.left_trim_dragging or self.right_trim_dragging:
            print(f"🔍 裁剪拖拽中: left={self.left_trim_dragging}, right={self.right_trim_dragging}")

            if self.trim_drag_start_pos:
                delta_x = event.pos().x() - self.trim_drag_start_pos.x()
                print(f"   - 拖拽偏移: delta_x={delta_x}")

                if self.left_trim_dragging:
                    # 左游标：从左边裁剪，支持负值（扩展）
                    # 🔧 修复：使用helper方法获取pixels_per_second
                    pixels_per_second = self.get_pixels_per_second()
                    max_extend = self.media_item.get('trim_start', 0) * pixels_per_second if isinstance(self.media_item, dict) else 0
                    old_preview = self.preview_left_trim_pos
                    self.preview_left_trim_pos = max(-max_extend, min(delta_x, self.width() - self.preview_right_trim_pos - 20))
                    print(f"   - 左游标预览: {old_preview} -> {self.preview_left_trim_pos} (max_extend={max_extend})")

                elif self.right_trim_dragging:
                    # 右游标：从右边裁剪，支持负值（扩展）
                    # 🔧 修复：使用helper方法获取pixels_per_second
                    pixels_per_second = self.get_pixels_per_second()
                    max_extend = self.media_item.get('trim_end', 0) * pixels_per_second if isinstance(self.media_item, dict) else 0
                    old_preview = self.preview_right_trim_pos
                    self.preview_right_trim_pos = max(-max_extend, min(-delta_x, self.width() - self.preview_left_trim_pos - 20))
                    print(f"   - 右游标预览: {old_preview} -> {self.preview_right_trim_pos} (max_extend={max_extend})")

                # 实时更新显示（只更新预览，不修改实际数据）
                self.update()

                # 🔧 修复：更新专门的游标绘制层
                # 这样可以保证拖拽时游标实时更新，显示层级正确
                parent = self.parent()
                if parent and hasattr(parent, 'update_trim_handles'):
                    try:
                        parent.update_trim_handles()
                    except:
                        pass  # 忽略调用错误

                return

        # 处理普通拖拽
        if (event.buttons() == Qt.MouseButton.LeftButton and
            self.drag_start_pos is not None):

            # 计算拖拽距离
            distance = (event.pos() - self.drag_start_pos).manhattanLength()

            if distance > 5:  # 开始拖拽
                self.dragging = True

                # 显示所有轨道的拖拽指示器
                self.show_all_drag_indicators()

                # 设置拖拽样式
                self.setStyleSheet("border: 2px solid #00C896;")

                # 启用透明背景以便拖拽时看到下方内容
                self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, True)
                self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, True)

                # 计算新位置
                new_pos = self.original_pos + (event.pos() - self.drag_start_pos)

                # 限制在父组件范围内
                parent_rect = self.parent().rect()
                new_pos.setX(max(0, min(new_pos.x(), parent_rect.width() - self.width())))
                new_pos.setY(max(0, min(new_pos.y(), parent_rect.height() - self.height())))

                self.move(new_pos)

                # 更新拖拽预览位置
                pixels_per_second = self.get_pixels_per_second()
                time_pos = new_pos.x() / pixels_per_second
                # 更新时间轴的拖拽预览线
                if hasattr(self.timeline, 'set_drag_position'):
                    self.timeline.set_drag_position(time_pos)

    def mouseReleaseEvent(self, event):
        """鼠标释放事件 - 完全按照原版逻辑"""
        # 处理裁剪游标释放 - 剪映风格应用模式
        if self.left_trim_dragging or self.right_trim_dragging:
            # 应用预览的裁剪到实际数据
            if isinstance(self.media_item, dict):
                self.apply_preview_trim()

            self.left_trim_dragging = False
            self.right_trim_dragging = False
            self.trim_drag_start_pos = None
            # 清除预览状态
            self.preview_left_trim_pos = 0
            self.preview_right_trim_pos = 0

            # 🔧 修复：更新专门的游标绘制层
            parent = self.parent()
            if parent and hasattr(parent, 'update_trim_handles'):
                try:
                    parent.update_trim_handles()
                except:
                    pass  # 忽略调用错误

            # 🔧 关键修复：重新绘制以显示裁剪效果
            self.update()

            print("✅ 裁剪完成，已应用到实际数据")
            return

        # 处理普通拖拽释放
        if self.dragging:
            self.dragging = False
            self.setStyleSheet("")  # 清除拖拽样式

            # 恢复正常绘制
            self.setAttribute(Qt.WidgetAttribute.WA_NoSystemBackground, False)
            self.setAttribute(Qt.WidgetAttribute.WA_TranslucentBackground, False)

            # 隐藏所有轨道的拖拽指示器
            self.hide_all_drag_indicators()

            # 更新媒体项的时间位置
            if isinstance(self.media_item, dict):
                pixels_per_second = self.get_pixels_per_second()
                new_time = self.x() / pixels_per_second

                # 应用磁性吸附
                if hasattr(self.timeline, 'apply_snap') and callable(self.timeline.apply_snap):
                    snapped_time = self.timeline.apply_snap(new_time)
                    # 更新媒体块位置到吸附后的位置
                    snapped_x = int(snapped_time * pixels_per_second)
                    self.move(snapped_x, self.y())
                    self.media_item['start_time'] = snapped_time
                    print(f"媒体块移动到时间: {new_time:.2f}s -> {snapped_time:.2f}s (磁性吸附)")
                else:
                    self.media_item['start_time'] = new_time
                    print(f"媒体块移动到时间: {new_time:.2f}s")

        # 🔧 点击处理已移至mousePressEvent，这里只处理拖拽结束

        self.drag_start_pos = None
        self.original_pos = None

    def is_on_left_trim_handle(self, pos):
        """检查鼠标是否在左游标上 - 剪映风格，支持预览"""
        # 安全检查：确保预览属性存在
        if not hasattr(self, 'preview_left_trim_pos'):
            self.preview_left_trim_pos = 0

        # 使用预览状态或实际状态
        left_trim = self.preview_left_trim_pos if self.left_trim_dragging else self.left_trim_pos

        # 获取素材块在轨道中的位置
        block_rect = self.geometry()
        block_x = block_rect.x()

        # 计算左游标在轨道坐标系中的位置
        if left_trim < 0:
            # 扩展状态：游标在素材块左边
            left_x = block_x + left_trim
        else:
            # 普通裁剪状态
            left_x = block_x + left_trim

        # 将鼠标位置转换为轨道坐标系
        track_pos = self.mapToParent(pos)

        # 增加检测区域，使游标更容易点击
        # 检测区域：游标位置前后各12像素，总共24像素宽度
        hit = (left_x - 12) <= track_pos.x() <= (left_x + 12)

        return hit

    def is_on_right_trim_handle(self, pos):
        """检查鼠标是否在右游标上 - 剪映风格，支持预览"""
        # 安全检查：确保预览属性存在
        if not hasattr(self, 'preview_right_trim_pos'):
            self.preview_right_trim_pos = 0

        # 使用预览状态或实际状态
        right_trim = self.preview_right_trim_pos if self.right_trim_dragging else self.right_trim_pos

        # 获取素材块在轨道中的位置
        block_rect = self.geometry()
        block_x = block_rect.x()
        block_width = block_rect.width()

        # 计算右游标在轨道坐标系中的位置
        if right_trim < 0:
            # 扩展状态：游标在素材块右边
            right_x = block_x + block_width - right_trim
        else:
            # 普通裁剪状态
            right_x = block_x + block_width - right_trim

        # 将鼠标位置转换为轨道坐标系
        track_pos = self.mapToParent(pos)

        # 增加检测区域，使游标更容易点击
        # 检测区域：游标位置前后各12像素，总共24像素宽度
        hit = (right_x - 12) <= track_pos.x() <= (right_x + 12)

        return hit

    def save_trim_state(self):
        """保存当前裁剪状态到历史记录"""
        state = {
            'left_trim_pos': self.left_trim_pos,
            'right_trim_pos': self.right_trim_pos
        }
        self.trim_history.append(state)
        self.current_trim_index = len(self.trim_history) - 1

    def apply_preview_trim(self):
        """应用预览的裁剪到实际数据"""
        if not isinstance(self.media_item, dict):
            return

        # 检查是否是恢复操作（往外拖）
        is_restore_left = self.preview_left_trim_pos < 0
        is_restore_right = self.preview_right_trim_pos < 0

        if is_restore_left or is_restore_right:
            # 恢复裁剪的视觉提示
            self.show_restore_hint(is_restore_left, is_restore_right)

        # 将预览状态应用到实际状态
        self.left_trim_pos = self.preview_left_trim_pos
        self.right_trim_pos = self.preview_right_trim_pos

        # 应用裁剪
        self.apply_trim_to_media()

        print(f"应用预览裁剪: 左={self.left_trim_pos}px, 右={self.right_trim_pos}px")

    def apply_trim_to_media(self):
        """将像素位置的裁剪应用到媒体项的时间数据 - 完全按照原版逻辑"""
        if not isinstance(self.media_item, dict) or not hasattr(self.timeline, 'pixels_per_second'):
            return

        # 保存原始时长，用于还原
        if 'original_duration' not in self.media_item:
            self.media_item['original_duration'] = self.media_item['duration']

        # 将像素位置转换为时间偏移
        pixels_per_second = self.get_pixels_per_second()
        left_time_offset = self.left_trim_pos / pixels_per_second
        right_time_offset = self.right_trim_pos / pixels_per_second

        # 更新媒体项的裁剪信息
        current_trim_start = self.media_item.get('trim_start', 0)
        current_trim_end = self.media_item.get('trim_end', 0)

        # 应用新的裁剪
        self.media_item['trim_start'] = current_trim_start + left_time_offset
        self.media_item['trim_end'] = current_trim_end + right_time_offset

        # 计算新时长
        original_duration = self.media_item.get('original_duration', self.media_item['duration'])
        new_duration = original_duration - self.media_item['trim_start'] - self.media_item['trim_end']
        self.media_item['duration'] = max(0.1, new_duration)  # 最小0.1秒

        # 🔧 关键修复：更新媒体块的物理宽度，让它变短
        new_width = max(80, int(self.media_item['duration'] * pixels_per_second))
        current_pos = self.pos()

        print(f"🔧 裁剪前宽度: {self.width()}px, 新时长: {self.media_item['duration']:.2f}s")
        print(f"🔧 计算新宽度: {new_width}px (像素比={pixels_per_second})")

        # 🔧 修复：不要移动位置，只改变宽度
        # 保持左边缘位置不变，只缩短右边缘
        self.setGeometry(current_pos.x(), current_pos.y(), new_width, self.height())

        # 重置裁剪状态
        self.left_trim_pos = 0
        self.right_trim_pos = 0

        print(f"应用裁剪到媒体: trim_start={self.media_item['trim_start']:.2f}s, trim_end={self.media_item['trim_end']:.2f}s, new_duration={self.media_item['duration']:.2f}s")
        print(f"🔧 媒体块宽度更新: {self.width()} -> {new_width}px")

        # 强制重新绘制
        self.update()

    def reset_trim(self):
        """完全重置裁剪，恢复到原始状态"""
        if not isinstance(self.media_item, dict):
            return

        # 恢复原始时长
        if 'original_duration' in self.media_item:
            self.media_item['duration'] = self.media_item['original_duration']

        # 清除裁剪信息
        self.media_item['trim_start'] = 0
        self.media_item['trim_end'] = 0

        # 重置裁剪位置
        self.left_trim_pos = 0
        self.right_trim_pos = 0
        self.preview_left_trim_pos = 0
        self.preview_right_trim_pos = 0

        # 恢复原始宽度
        pixels_per_second = self.get_pixels_per_second()
        original_width = max(80, int(self.media_item['duration'] * pixels_per_second))
        current_pos = self.pos()
        self.setGeometry(current_pos.x(), current_pos.y(), original_width, self.height())

        # 更新游标绘制层
        parent = self.parent()
        if parent and hasattr(parent, 'update_trim_handles'):
            try:
                parent.update_trim_handles()
            except:
                pass

        # 重新绘制
        self.update()

        print(f"🔄 已重置裁剪: 恢复到原始时长 {self.media_item['duration']:.2f}s, 宽度 {original_width}px")

    def show_context_menu(self, position):
        """显示右键菜单"""
        from PySide6.QtWidgets import QMenu, QMessageBox

        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: #1A1A1A;
                color: #FFFFFF;
                border: 1px solid #333333;
                border-radius: 6px;
            }
            QMenu::item {
                padding: 8px 16px;
                border-radius: 4px;
            }
            QMenu::item:selected {
                background-color: #00C896;
                color: #FFFFFF;
            }
        """)

        # 检查是否有裁剪
        has_trim = (self.left_trim_pos > 0 or self.right_trim_pos > 0 or
                   (isinstance(self.media_item, dict) and
                    (self.media_item.get('trim_start', 0) > 0 or self.media_item.get('trim_end', 0) > 0)))

        # 重置裁剪选项
        if has_trim:
            reset_action = menu.addAction("🔄 重置裁剪")
            reset_action.triggered.connect(self.reset_trim)
        else:
            reset_action = menu.addAction("🔄 重置裁剪 (无裁剪)")
            reset_action.setEnabled(False)

        menu.addSeparator()

        # 删除素材块
        delete_action = menu.addAction("🗑️ 删除素材")
        delete_action.triggered.connect(self.delete_media_block)

        # 显示菜单
        menu.exec(self.mapToGlobal(position))

    def delete_media_block(self):
        """删除素材块"""
        from PySide6.QtWidgets import QMessageBox

        # 确认删除
        reply = QMessageBox.question(
            self,
            "确认删除",
            "确定要删除这个素材块吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No,
            QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            # 从父轨道中移除
            parent = self.parent()
            if parent and hasattr(parent, 'remove_media_block'):
                parent.remove_media_block(self)
            else:
                # 直接删除自己
                self.deleteLater()
            print(f"🗑️ 已删除素材块: {self.media_item.get('file_path', 'Unknown') if isinstance(self.media_item, dict) else 'Unknown'}")

    def show_all_drag_indicators(self):
        """显示所有轨道的拖拽指示器"""
        # 向上查找MultiTrackTimeline
        timeline = self.timeline
        if hasattr(timeline, 'show_all_drag_indicators'):
            timeline.show_all_drag_indicators()

    def hide_all_drag_indicators(self):
        """隐藏所有轨道的拖拽指示器"""
        # 向上查找MultiTrackTimeline
        timeline = self.timeline
        if hasattr(timeline, 'hide_all_drag_indicators'):
            timeline.hide_all_drag_indicators()

    def show_restore_hint(self, is_restore_left, is_restore_right):
        """显示恢复裁剪的视觉提示"""
        from PySide6.QtWidgets import QLabel, QGraphicsOpacityEffect
        from PySide6.QtCore import QTimer, QPropertyAnimation, QEasingCurve
        from PySide6.QtGui import QFont

        # 创建提示标签
        hint_text = ""
        if is_restore_left and is_restore_right:
            hint_text = "🔄 恢复完整视频"
        elif is_restore_left:
            hint_text = "🔄 恢复左侧内容"
        elif is_restore_right:
            hint_text = "🔄 恢复右侧内容"

        hint_label = QLabel(hint_text, self.parent())
        hint_label.setStyleSheet("""
            QLabel {
                background-color: rgba(0, 200, 150, 200);
                color: white;
                border-radius: 15px;
                padding: 8px 16px;
                font-weight: bold;
            }
        """)

        # 设置字体
        font = QFont("Arial", 10, QFont.Weight.Bold)
        hint_label.setFont(font)
        hint_label.adjustSize()

        # 定位在媒体块上方
        block_pos = self.pos()
        hint_x = block_pos.x() + (self.width() - hint_label.width()) // 2
        hint_y = block_pos.y() - hint_label.height() - 10
        hint_label.move(hint_x, hint_y)

        # 显示提示
        hint_label.show()
        hint_label.raise_()

        # 添加淡出动画
        opacity_effect = QGraphicsOpacityEffect()
        hint_label.setGraphicsEffect(opacity_effect)

        # 2秒后自动消失
        def hide_hint():
            try:
                hint_label.deleteLater()
            except:
                pass

        QTimer.singleShot(2000, hide_hint)
